<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a24956d1-faf1-4f53-8ee9-4a5385b15c4f" name="Changes" comment="删除了多余测试代码">
      <change beforePath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/AiRecommendVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/AiRecommendVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/AiRecommendServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/AiRecommendServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zlaDFtm8tmqnQn6W6u7IuzAN2y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.SkyApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/Document/Java全栈开发/05阶段：微服务框架/01-MybatisPlus/资料/mp-demo/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Problems",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="SkyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sky-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sky.SkyApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a24956d1-faf1-4f53-8ee9-4a5385b15c4f" name="Changes" comment="" />
      <created>1742690769155</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742690769155</updated>
      <workItem from="1742690772365" duration="12000" />
      <workItem from="1746793171306" duration="414000" />
      <workItem from="1750491946389" duration="56000" />
      <workItem from="1752307994188" duration="435000" />
      <workItem from="1752308443162" duration="175000" />
      <workItem from="1752308688407" duration="329000" />
      <workItem from="1752309023848" duration="1721000" />
      <workItem from="1752367981831" duration="1282000" />
      <workItem from="1752373461548" duration="897000" />
      <workItem from="1752397006853" duration="5000" />
      <workItem from="1753241242181" duration="4728000" />
      <workItem from="1753320155674" duration="1268000" />
      <workItem from="1753778830101" duration="2776000" />
      <workItem from="1753929827852" duration="2867000" />
      <workItem from="1753934669922" duration="11241000" />
      <workItem from="1754032413506" duration="2621000" />
      <workItem from="1754035311976" duration="115000" />
      <workItem from="1754035492202" duration="6533000" />
      <workItem from="1754058880072" duration="28000" />
      <workItem from="1754102015245" duration="7000" />
      <workItem from="1754140482589" duration="607000" />
      <workItem from="1754217607220" duration="4406000" />
    </task>
    <task id="LOCAL-00001" summary="新增ai查询数据库推荐菜品功能">
      <option name="closed" value="true" />
      <created>1753961233110</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753961233110</updated>
    </task>
    <task id="LOCAL-00002" summary="修复了修改菜品数据没有做空判断，口味不填会出bug的问题">
      <option name="closed" value="true" />
      <created>1754037125697</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754037125698</updated>
    </task>
    <task id="LOCAL-00003" summary="删除了多余测试代码">
      <option name="closed" value="true" />
      <created>1754037362146</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754037362146</updated>
    </task>
    <task id="LOCAL-00004" summary="删除了多余测试代码">
      <option name="closed" value="true" />
      <created>1754051984467</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754051984467</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="新增ai查询数据库推荐菜品功能" />
    <MESSAGE value="修复了修改菜品数据没有做空判断，口味不填会出bug的问题" />
    <MESSAGE value="删除了多余测试代码" />
    <option name="LAST_COMMIT_MESSAGE" value="删除了多余测试代码" />
  </component>
</project>